/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as Cohere from "../../api/index";
import * as core from "../../core";
export declare const ChatStreamRequestCitationQuality: core.serialization.Schema<serializers.ChatStreamRequestCitationQuality.Raw, Cohere.ChatStreamRequestCitationQuality>;
export declare namespace ChatStreamRequestCitationQuality {
    type Raw = "fast" | "accurate" | "off";
}
