"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const base_1 = __importDefault(require("./base"));
const config = [
    ...base_1.default,
    {
        name: 'svelte:prettier:turn-off-rules',
        rules: {
            // eslint-plugin-svelte rules
            'svelte/first-attribute-linebreak': 'off',
            'svelte/html-closing-bracket-new-line': 'off',
            'svelte/html-closing-bracket-spacing': 'off',
            'svelte/html-quotes': 'off',
            'svelte/html-self-closing': 'off',
            'svelte/indent': 'off',
            'svelte/max-attributes-per-line': 'off',
            'svelte/mustache-spacing': 'off',
            'svelte/no-spaces-around-equal-signs-in-attribute': 'off',
            'svelte/no-trailing-spaces': 'off',
            'svelte/shorthand-attribute': 'off',
            'svelte/shorthand-directive': 'off'
        }
    }
];
exports.default = config;
